<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xelvra P2P Messenger - Digital Freedom Manifest</title>
    <meta name="description" content="Secure, decentralized peer-to-peer communication platform built on end-to-end encryption">
    <meta name="keywords" content="P2P, messenger, encryption, decentralized, privacy, security">
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-container">
            <div class="nav-brand">
                <span class="brand-icon">🚀</span>
                <span class="brand-text">Xelvra</span>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="#hero" class="nav-link">Home</a>
                <a href="#features" class="nav-link">Features</a>
                <a href="#roadmap" class="nav-link">Roadmap</a>
                <a href="#tech" class="nav-link">Technology</a>
                <a href="#download" class="nav-link">Download</a>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="title-main">Messenger Xelvra</span>
                    <span class="title-sub">Digital Freedom Manifest</span>
                </h1>
                <p class="hero-description">
                    A secure, decentralized peer-to-peer communication platform built on 
                    end-to-end encryption. Restore privacy, security, and user control 
                    over digital communication.
                </p>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">&lt;20MB</span>
                        <span class="stat-label">Memory Usage</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">&lt;50ms</span>
                        <span class="stat-label">Latency</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">100%</span>
                        <span class="stat-label">Open Source</span>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="https://github.com/Xelvra/peerchat" class="btn btn-primary" target="_blank">
                        <span>View on GitHub</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                        </svg>
                    </a>
                    <a href="#download" class="btn btn-secondary">
                        <span>Download CLI</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="terminal-window">
                    <div class="terminal-header">
                        <div class="terminal-buttons">
                            <span class="btn-close"></span>
                            <span class="btn-minimize"></span>
                            <span class="btn-maximize"></span>
                        </div>
                        <div class="terminal-title">peerchat-cli</div>
                    </div>
                    <div class="terminal-body">
                        <div class="terminal-line">
                            <span class="prompt">$</span>
                            <span class="command">./peerchat-cli start</span>
                        </div>
                        <div class="terminal-line output">
                            <span>🚀 Starting Xelvra P2P Messenger CLI</span>
                        </div>
                        <div class="terminal-line output">
                            <span>✅ P2P node started successfully!</span>
                        </div>
                        <div class="terminal-line output">
                            <span>🆔 Your Peer ID: 12D3KooW...</span>
                        </div>
                        <div class="terminal-line output">
                            <span>📡 Listening on QUIC/TCP</span>
                        </div>
                        <div class="terminal-line">
                            <span class="prompt">></span>
                            <span class="cursor">_</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Why Messenger Xelvra?</h2>
                <p>True digital freedom through decentralized communication</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔐</div>
                    <h3>End-to-End Encryption</h3>
                    <p>Signal Protocol implementation with X3DH handshake and Double Ratchet. Your messages are encrypted before leaving your device.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3>Decentralized Network</h3>
                    <p>Direct P2P connections with intelligent relay fallback. No central servers, no single point of failure.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Ultra Performance</h3>
                    <p>QUIC transport for ultra-low latency. &lt;20MB memory usage, &lt;1% CPU in idle mode.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>Privacy First</h3>
                    <p>Zero-knowledge architecture. Your data stays on your devices, under your control.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3>Open Source</h3>
                    <p>Every line of code is open for inspection and verification. Complete transparency.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🚫</div>
                    <h3>Censorship Resistant</h3>
                    <p>No central authority can block or monitor your communications. True digital freedom.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Section -->
    <section id="tech" class="tech">
        <div class="container">
            <div class="section-header">
                <h2>Technology Stack</h2>
                <p>Built with modern, proven technologies</p>
            </div>
            <div class="tech-grid">
                <div class="tech-category">
                    <h3>🔧 Core Infrastructure</h3>
                    <ul>
                        <li>libp2p integration with QUIC/TCP transports</li>
                        <li>P2P node initialization and graceful shutdown</li>
                        <li>Real-time P2P networking</li>
                    </ul>
                </div>
                <div class="tech-category">
                    <h3>🌐 Network Discovery</h3>
                    <ul>
                        <li>mDNS peer discovery</li>
                        <li>UDP broadcast for local networks</li>
                        <li>STUN integration for NAT traversal</li>
                    </ul>
                </div>
                <div class="tech-category">
                    <h3>📡 Transport Layer</h3>
                    <ul>
                        <li>QUIC as primary protocol</li>
                        <li>TCP fallback for reliability</li>
                        <li>UDP buffer optimization</li>
                    </ul>
                </div>
                <div class="tech-category">
                    <h3>💬 Messaging & Files</h3>
                    <ul>
                        <li>Secure P2P file transfer</li>
                        <li>File chunking and integrity verification</li>
                        <li>Progress tracking and resume capability</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Roadmap Section -->
    <section id="roadmap" class="roadmap">
        <div class="container">
            <div class="section-header">
                <h2>Development Roadmap</h2>
                <p>Journey through the epochs of digital freedom</p>
            </div>
            <div class="roadmap-highway">
                <!-- Highway Road -->
                <div class="highway-road">
                    <div class="road-line"></div>
                    <div class="road-markers">
                        <div class="road-marker"></div>
                        <div class="road-marker"></div>
                        <div class="road-marker"></div>
                        <div class="road-marker"></div>
                        <div class="road-marker"></div>
                        <div class="road-marker"></div>
                    </div>
                </div>

                <!-- Epoch Billboards -->
                <div class="epoch-billboard epoch-current" data-epoch="01">
                    <div class="billboard-post"></div>
                    <div class="billboard-content">
                        <div class="epoch-badge">
                            <span class="epoch-number">01</span>
                            <span class="epoch-progress">85%</span>
                        </div>
                        <h3>CLI Foundation</h3>
                        <p>Command-line interface serves as the foundation for all core P2P functionality.</p>
                        <div class="feature-grid">
                            <span class="feature-tag done">✅ P2P Core</span>
                            <span class="feature-tag done">✅ Discovery</span>
                            <span class="feature-tag done">✅ NAT Traversal</span>
                            <span class="feature-tag done">✅ File Transfer</span>
                            <span class="feature-tag done">✅ CLI Commands</span>
                            <span class="feature-tag progress">🔄 Chat UI</span>
                        </div>
                    </div>
                </div>

                <div class="epoch-billboard epoch-planned" data-epoch="02">
                    <div class="billboard-post"></div>
                    <div class="billboard-content">
                        <div class="epoch-badge">
                            <span class="epoch-number">02</span>
                            <span class="epoch-status">Planned</span>
                        </div>
                        <h3>API Service</h3>
                        <p>Local gRPC API service to bridge P2P core with frontend applications.</p>
                        <div class="feature-grid">
                            <span class="feature-tag planned">📋 gRPC Server</span>
                            <span class="feature-tag planned">📋 Database Layer</span>
                            <span class="feature-tag planned">📋 Monitoring</span>
                            <span class="feature-tag planned">📋 Rate Limiting</span>
                            <span class="feature-tag planned">📋 Streaming</span>
                        </div>
                    </div>
                </div>

                <div class="epoch-billboard epoch-planned" data-epoch="03">
                    <div class="billboard-post"></div>
                    <div class="billboard-content">
                        <div class="epoch-badge">
                            <span class="epoch-number">03</span>
                            <span class="epoch-status">Planned</span>
                        </div>
                        <h3>GUI Application</h3>
                        <p>Cross-platform Flutter application with mobile optimization.</p>
                        <div class="feature-grid">
                            <span class="feature-tag planned">📋 Modern UI/UX</span>
                            <span class="feature-tag planned">📋 Onboarding</span>
                            <span class="feature-tag planned">📋 Energy Optimized</span>
                            <span class="feature-tag planned">📋 Multi-Platform</span>
                            <span class="feature-tag planned">📋 Group Chats</span>
                        </div>
                    </div>
                </div>

                <div class="epoch-billboard epoch-future" data-epoch="04">
                    <div class="billboard-post"></div>
                    <div class="billboard-content">
                        <div class="epoch-badge">
                            <span class="epoch-number">04</span>
                            <span class="epoch-status">Future</span>
                        </div>
                        <h3>Advanced Features</h3>
                        <p>Advanced capabilities and ecosystem expansion.</p>
                        <div class="feature-grid">
                            <span class="feature-tag future">🔮 Zero-Knowledge</span>
                            <span class="feature-tag future">🔮 Quantum Resistant</span>
                            <span class="feature-tag future">🔮 Voice & Video</span>
                            <span class="feature-tag future">🔮 Mesh Networks</span>
                            <span class="feature-tag future">🔮 Token Ecosystem</span>
                        </div>
                    </div>
                </div>

                <!-- Highway Destination -->
                <div class="highway-destination">
                    <div class="destination-sign">
                        <h4>🏁 Digital Freedom</h4>
                        <p>Complete P2P Communication Platform</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="download">
        <div class="container">
            <div class="section-header">
                <h2>Get Started</h2>
                <p>Download and start using Xelvra P2P Messenger today</p>
            </div>
            <div class="download-grid">
                <div class="download-card">
                    <div class="download-header">
                        <h3>🚀 CLI Version</h3>
                        <span class="version-badge">v0.1.0-alpha</span>
                    </div>
                    <p>Command-line interface for developers and power users. Full P2P functionality available now.</p>
                    <div class="download-stats">
                        <div class="stat">
                            <span class="stat-label">Memory</span>
                            <span class="stat-value">&lt;20MB</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">CPU</span>
                            <span class="stat-value">&lt;1%</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Latency</span>
                            <span class="stat-value">&lt;50ms</span>
                        </div>
                    </div>
                    <div class="download-actions">
                        <a href="https://github.com/Xelvra/peerchat/releases" class="btn btn-primary" target="_blank">
                            <span>Download CLI</span>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                            </svg>
                        </a>
                        <a href="https://github.com/Xelvra/peerchat" class="btn btn-secondary" target="_blank">
                            <span>View Source</span>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <div class="download-card coming-soon">
                    <div class="download-header">
                        <h3>📱 GUI Version</h3>
                        <span class="version-badge coming-soon">Coming Soon</span>
                    </div>
                    <p>Cross-platform Flutter application with modern UI. Mobile-optimized with energy-efficient design.</p>
                    <div class="coming-soon-features">
                        <ul>
                            <li>📱 Android & iOS Support</li>
                            <li>🖥️ Desktop (Linux, macOS, Windows)</li>
                            <li>⚡ Energy Optimized (&lt;100mW)</li>
                            <li>🎨 Modern Material Design</li>
                        </ul>
                    </div>
                    <div class="download-actions">
                        <button class="btn btn-secondary" disabled>
                            <span>In Development</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="quick-start">
                <h3>Quick Start Guide</h3>
                <div class="code-block">
                    <div class="code-header">
                        <span>Terminal</span>
                        <button class="copy-btn" onclick="copyCode()">Copy</button>
                    </div>
                    <pre><code id="quick-start-code"># Clone and build
git clone https://github.com/Xelvra/peerchat.git
cd peerchat
go build -o bin/peerchat-cli cmd/peerchat-cli/main.go

# Initialize and start
./bin/peerchat-cli init
./bin/peerchat-cli start</code></pre>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="brand">
                        <span class="brand-icon">🚀</span>
                        <span class="brand-text">Xelvra</span>
                    </div>
                    <p>Digital Freedom Manifest</p>
                </div>
                <div class="footer-links">
                    <div class="link-group">
                        <h4>Project</h4>
                        <a href="https://github.com/Xelvra/peerchat" target="_blank">GitHub</a>
                        <a href="https://github.com/Xelvra/peerchat/releases" target="_blank">Releases</a>
                        <a href="https://github.com/Xelvra/peerchat/issues" target="_blank">Issues</a>
                    </div>
                    <div class="link-group">
                        <h4>Documentation</h4>
                        <a href="https://github.com/Xelvra/peerchat/blob/main/docs/USER_GUIDE.md" target="_blank">User Guide</a>
                        <a href="https://github.com/Xelvra/peerchat/blob/main/docs/DEVELOPER_GUIDE.md" target="_blank">Developer Guide</a>
                        <a href="https://github.com/Xelvra/peerchat/blob/main/docs/API_REFERENCE.md" target="_blank">API Reference</a>
                    </div>
                    <div class="link-group">
                        <h4>Community</h4>
                        <a href="https://github.com/Xelvra/peerchat/discussions" target="_blank">Discussions</a>
                        <a href="https://github.com/Xelvra/peerchat/blob/main/docs/CONTRIBUTING.md" target="_blank">Contributing</a>
                        <a href="https://github.com/Xelvra/peerchat/blob/main/LICENSE" target="_blank">License</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Xelvra P2P Messenger. Licensed under AGPLv3.</p>
                <p>Built with ❤️ for digital freedom and privacy.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
