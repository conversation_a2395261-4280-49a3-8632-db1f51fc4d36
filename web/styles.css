/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --color-bg: #0a0a0a;
    --color-bg-secondary: #111111;
    --color-bg-tertiary: #1a1a1a;
    --color-text: #ffffff;
    --color-text-secondary: #a0a0a0;
    --color-text-muted: #666666;
    --color-accent: #0088ff;
    --color-accent-hover: #0066cc;
    --color-border: #333333;
    --color-border-light: #444444;
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    
    /* Layout */
    --container-max-width: 1200px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-primary);
    background-color: var(--color-bg);
    color: var(--color-text);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Navigation */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--color-border);
    z-index: 1000;
    transition: var(--transition-normal);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 700;
    font-size: 1.25rem;
}

.brand-icon {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--color-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--color-accent);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--color-accent);
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 2px;
    background: var(--color-text);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-2xl) 0;
    background: linear-gradient(135deg, var(--color-bg) 0%, var(--color-bg-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 136, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 136, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.hero-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-md);
}

.title-main {
    display: block;
    background: linear-gradient(135deg, var(--color-text) 0%, var(--color-accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-sub {
    display: block;
    font-size: 0.6em;
    color: var(--color-text-secondary);
    font-weight: 400;
    margin-top: var(--spacing-xs);
}

.hero-description {
    font-size: 1.25rem;
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-accent);
    font-family: var(--font-mono);
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: var(--color-text-muted);
    margin-top: var(--spacing-xs);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-fast);
    border: 2px solid transparent;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--color-accent);
    color: var(--color-bg);
}

.btn-primary:hover {
    background: var(--color-accent-hover);
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: var(--color-text);
    border-color: var(--color-border-light);
}

.btn-secondary:hover {
    border-color: var(--color-accent);
    color: var(--color-accent);
    transform: translateY(-2px);
}

/* Terminal Window */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.terminal-window {
    background: var(--color-bg-tertiary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 500px;
}

.terminal-header {
    background: var(--color-bg-secondary);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    border-bottom: 1px solid var(--color-border);
}

.terminal-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.terminal-buttons span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.btn-close { background: #ff5f56; }
.btn-minimize { background: #ffbd2e; }
.btn-maximize { background: #27ca3f; }

.terminal-title {
    font-family: var(--font-mono);
    font-size: 0.875rem;
    color: var(--color-text-secondary);
}

.terminal-body {
    padding: var(--spacing-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.5;
}

.terminal-line {
    margin-bottom: var(--spacing-xs);
}

.prompt {
    color: var(--color-accent);
    margin-right: var(--spacing-xs);
}

.command {
    color: var(--color-text);
}

.output {
    color: var(--color-text-secondary);
    padding-left: var(--spacing-md);
}

.cursor {
    color: var(--color-accent);
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Section Header */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: linear-gradient(135deg, var(--color-text) 0%, var(--color-accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    font-size: 1.25rem;
    color: var(--color-text-secondary);
}

/* Features Section */
.features {
    padding: var(--spacing-2xl) 0;
    background: var(--color-bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.feature-card {
    background: var(--color-bg-tertiary);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
    transition: var(--transition-normal);
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: var(--color-accent);
    box-shadow: 0 10px 30px rgba(0, 136, 255, 0.1);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
}

.feature-card p {
    color: var(--color-text-secondary);
    line-height: 1.7;
}

/* Technology Section */
.tech {
    padding: var(--spacing-2xl) 0;
    background: var(--color-bg);
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.tech-category {
    background: var(--color-bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
}

.tech-category h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
}

.tech-category ul {
    list-style: none;
}

.tech-category li {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-xs);
    padding-left: var(--spacing-md);
    position: relative;
}

.tech-category li::before {
    content: '▸';
    position: absolute;
    left: 0;
    color: var(--color-accent);
}

/* Roadmap Section */
.roadmap {
    padding: var(--spacing-2xl) 0;
    background: var(--color-bg-secondary);
    position: relative;
    overflow: hidden;
}

.roadmap::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(0, 136, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(0, 136, 255, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.roadmap-highway {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-2xl) 0;
}

/* Highway Road */
.highway-road {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 120px;
    transform: translateX(-50%);
    background: linear-gradient(180deg,
        var(--color-border-light) 0%,
        var(--color-border) 50%,
        var(--color-border-light) 100%);
    border-radius: 60px;
    z-index: 1;
    /* Wavy road effect */
    clip-path: polygon(
        20% 0%, 80% 0%,
        85% 10%, 75% 20%,
        85% 30%, 75% 40%,
        85% 50%, 75% 60%,
        85% 70%, 75% 80%,
        85% 90%, 80% 100%,
        20% 100%, 15% 90%,
        25% 80%, 15% 70%,
        25% 60%, 15% 50%,
        25% 40%, 15% 30%,
        25% 20%, 15% 10%
    );
}

.road-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: repeating-linear-gradient(
        to top,
        var(--color-text-muted) 0px,
        var(--color-text-muted) 20px,
        transparent 20px,
        transparent 40px
    );
    transform: translateX(-50%);
    animation: roadFlow 3s linear infinite;
}

.road-cars {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    transform: translateX(-50%);
    width: 100%;
}

.road-car {
    position: absolute;
    font-size: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: carDrive 8s ease-in-out infinite;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.car-red {
    color: #ff4444;
}

.car-yellow {
    color: #ffdd44;
}

.road-car:nth-child(1) {
    top: 10%;
    animation-delay: 0s;
    animation-duration: 8s;
}
.road-car:nth-child(2) {
    top: 35%;
    animation-delay: 2s;
    animation-duration: 10s;
}
.road-car:nth-child(3) {
    top: 60%;
    animation-delay: 4s;
    animation-duration: 9s;
}
.road-car:nth-child(4) {
    top: 85%;
    animation-delay: 6s;
    animation-duration: 7s;
}

@keyframes roadFlow {
    0% { background-position: 0 0; }
    100% { background-position: 0 -40px; }
}

@keyframes carDrive {
    0% {
        transform: translateX(-50%) translateY(0px) rotate(0deg);
        opacity: 0.8;
    }
    25% {
        transform: translateX(-30%) translateY(-10px) rotate(5deg);
        opacity: 1;
    }
    50% {
        transform: translateX(-70%) translateY(5px) rotate(-3deg);
        opacity: 1;
    }
    75% {
        transform: translateX(-40%) translateY(-5px) rotate(2deg);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(0px) rotate(0deg);
        opacity: 0.8;
    }
}

/* Epoch Billboards */
.epoch-billboard {
    position: relative;
    margin: var(--spacing-2xl) 0;
    display: flex;
    align-items: center;
    z-index: 2;
}

.epoch-billboard:nth-child(even) {
    flex-direction: row-reverse;
}

.epoch-billboard:nth-child(even) .billboard-content {
    margin-left: 0;
    margin-right: calc(50% + 80px);
}

.billboard-post {
    position: absolute;
    width: 8px;
    height: 80px;
    background: var(--color-border-light);
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}

.epoch-billboard:nth-child(even) .billboard-post {
    left: 50%;
    transform: translateX(-50%);
}

.billboard-content {
    background: var(--color-bg-tertiary);
    border: 2px solid var(--color-border);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-left: calc(50% + 80px);
    width: 400px;
    position: relative;
    transition: var(--transition-normal);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.billboard-content::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -12px;
    width: 0;
    height: 0;
    border: 12px solid transparent;
    border-right-color: var(--color-border);
    transform: translateY(-50%);
}

.billboard-content::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -10px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-right-color: var(--color-bg-tertiary);
    transform: translateY(-50%);
}

.epoch-billboard:nth-child(even) .billboard-content::before {
    left: auto;
    right: -12px;
    border-right-color: transparent;
    border-left-color: var(--color-border);
}

.epoch-billboard:nth-child(even) .billboard-content::after {
    left: auto;
    right: -10px;
    border-right-color: transparent;
    border-left-color: var(--color-bg-tertiary);
}

.billboard-content:hover {
    transform: translateY(-5px);
    border-color: var(--color-accent);
    box-shadow: 0 10px 30px rgba(0, 136, 255, 0.2);
}

.epoch-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.epoch-number {
    font-family: var(--font-mono);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-accent);
    line-height: 1;
}

.epoch-progress {
    background: var(--color-accent);
    color: var(--color-bg);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.epoch-status {
    background: var(--color-border);
    color: var(--color-text-secondary);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.billboard-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--color-text);
}

.billboard-content p {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.feature-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.feature-tag {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.feature-tag.done {
    background: rgba(0, 136, 255, 0.2);
    color: var(--color-accent);
}

.feature-tag.progress {
    background: rgba(255, 189, 46, 0.2);
    color: #ffbd2e;
}

.feature-tag.planned {
    background: rgba(160, 160, 160, 0.2);
    color: var(--color-text-secondary);
}

.feature-tag.future {
    background: rgba(102, 102, 102, 0.2);
    color: var(--color-text-muted);
}

/* Highway Destination */
.highway-destination {
    position: relative;
    text-align: center;
    margin-top: var(--spacing-2xl);
    z-index: 2;
}

.destination-sign {
    background: var(--color-bg-tertiary);
    border: 2px solid var(--color-accent);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    display: inline-block;
    box-shadow: 0 5px 20px rgba(0, 136, 255, 0.3);
    animation: destinationGlow 3s ease-in-out infinite;
}

.destination-sign h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--color-accent);
}

.destination-sign p {
    color: var(--color-text-secondary);
    margin: 0;
}

@keyframes destinationGlow {
    0%, 100% {
        box-shadow: 0 5px 20px rgba(0, 136, 255, 0.3);
    }
    50% {
        box-shadow: 0 5px 30px rgba(0, 136, 255, 0.5);
    }
}

/* Download Section */
.download {
    padding: var(--spacing-2xl) 0;
    background: var(--color-bg);
}

.download-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.download-card {
    background: var(--color-bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
}

.download-card:hover {
    transform: translateY(-5px);
    border-color: var(--color-accent);
    box-shadow: 0 10px 30px rgba(0, 136, 255, 0.1);
}

.download-card.coming-soon {
    opacity: 0.7;
}

.download-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.download-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

.version-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    background: var(--color-accent);
    color: var(--color-bg);
}

.version-badge.coming-soon {
    background: var(--color-text-muted);
    color: var(--color-text);
}

.download-card p {
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

.download-stats {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--color-bg-tertiary);
    border-radius: var(--border-radius);
}

.download-stats .stat {
    text-align: center;
    flex: 1;
}

.download-stats .stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--color-text-muted);
    margin-bottom: 4px;
}

.download-stats .stat-value {
    display: block;
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--color-accent);
}

.coming-soon-features ul {
    list-style: none;
    margin-bottom: var(--spacing-lg);
}

.coming-soon-features li {
    padding: var(--spacing-xs) 0;
    color: var(--color-text-secondary);
}

.download-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Quick Start */
.quick-start {
    background: var(--color-bg-secondary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
    padding: var(--spacing-xl);
}

.quick-start h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.code-block {
    background: var(--color-bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--color-border);
    overflow: hidden;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-border);
    font-size: 0.875rem;
    color: var(--color-text-secondary);
}

.copy-btn {
    background: transparent;
    border: 1px solid var(--color-border);
    color: var(--color-text-secondary);
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;
    transition: var(--transition-fast);
}

.copy-btn:hover {
    border-color: var(--color-accent);
    color: var(--color-accent);
}

.code-block pre {
    margin: 0;
    padding: var(--spacing-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--color-text);
    overflow-x: auto;
}

.code-block code {
    color: var(--color-text);
}

/* Footer */
.footer {
    background: var(--color-bg-secondary);
    border-top: 1px solid var(--color-border);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
}

.footer-brand .brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xs);
}

.footer-brand p {
    color: var(--color-text-secondary);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.link-group h4 {
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--color-text);
}

.link-group a {
    display: block;
    color: var(--color-text-secondary);
    text-decoration: none;
    margin-bottom: var(--spacing-xs);
    transition: var(--transition-fast);
}

.link-group a:hover {
    color: var(--color-accent);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--color-border);
    color: var(--color-text-muted);
}

.footer-bottom p {
    margin-bottom: var(--spacing-xs);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .hero-actions {
        justify-content: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .tech-grid {
        grid-template-columns: 1fr;
    }

    .download-grid {
        grid-template-columns: 1fr;
    }

    .download-stats {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-links {
        grid-template-columns: 1fr;
        text-align: left;
    }

    /* Roadmap Mobile */
    .roadmap-highway {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .highway-road {
        width: 80px;
    }

    .billboard-content {
        width: 100%;
        margin-left: 0 !important;
        margin-right: 0 !important;
        margin-bottom: var(--spacing-lg);
    }

    .epoch-billboard {
        flex-direction: column !important;
        align-items: center;
    }

    .billboard-content::before,
    .billboard-content::after {
        display: none;
    }

    .billboard-post {
        height: 40px;
    }

    .feature-grid {
        justify-content: center;
    }

    .epoch-number {
        font-size: 2rem;
    }
}
